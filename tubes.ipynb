import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler

datas = pd.read_csv('BankChurners.csv')
cleanDatas = datas.copy()

cleanDatas.info()

cleanDatas.drop(['Naive_Bayes_Classifier_Attrition_Flag_Card_Category_Contacts_Count_12_mon_Dependent_count_Education_Level_Months_Inactive_12_mon_2','Naive_Bayes_Classifier_Attrition_Flag_Card_Category_Contacts_Count_12_mon_Dependent_count_Education_Level_Months_Inactive_12_mon_1'],axis= 1,inplace=True)

missing_values = cleanDatas.isnull().sum()
missing_values[missing_values>0]

cleanDatas.isnull().sum()

duplicated_value = cleanDatas.duplicated().sum()
duplicated_value[duplicated_value>0]

cleanDatas.duplicated().sum()

cleanDatas['Income_Category'].value_counts()

def clean_col(x):
        if 'K' in x:
            return x.replace('K','').replace('$','')
        elif '+' in x:
            return x.replace('+','')
        elif x =='Less than 40':
            return x.split()[2]
        return x

cleanDatas['Income_Category'] = datas['Income_Category'].apply(clean_col)
cleanDatas['Income_Category'].value_counts()

def aver_value(x):
    if '-' in x:
        splitted_values=x.split('-')
        first_value= int(splitted_values[0])
        second_value = int(splitted_values[1])
        average = (first_value + second_value)/2
        return average
    else:
        return x

cleanDatas['Income_Category'] = datas['Income_Category'].apply(clean_col).apply(aver_value)
cleanDatas['Income_Category'] = pd.to_numeric(cleanDatas['Income_Category'], errors='coerce')
print(cleanDatas['Income_Category'].value_counts(dropna=False))

datas['Income_Category'].dtype

datas.isnull().sum()

numeric_columns = cleanDatas.select_dtypes(include=['number'])
for feature in numeric_columns.columns:
    plt.figure(figsize=(10, 6))
    sns.boxplot(x=cleanDatas[feature])
    plt.title(f'Box Plot of {feature}')
    plt.show()

num_cols = numeric_columns.columns
Q1 = cleanDatas[num_cols].quantile(0.25)
Q3 = cleanDatas[num_cols].quantile(0.75)
IQR = Q3 - Q1

# Filter dataframe untuk hanya menyimpan baris yang tidak mengandung outliers pada kolom numerik
condition = ~((cleanDatas[num_cols] < (Q1 - 1.5 * IQR)) | (cleanDatas[num_cols] > (Q3 + 1.5 * IQR))).any(axis=1)
df_filtered_numeric = cleanDatas.loc[condition, num_cols]
# Menggabungkan kembali dengan kolom kategorikal
categorical_features = cleanDatas.select_dtypes(include=['object']).columns
df = pd.concat([df_filtered_numeric, cleanDatas.loc[condition, categorical_features]], axis=1)

for feature in df_filtered_numeric:
    plt.figure(figsize=(10, 6))
    sns.boxplot(x=df_filtered_numeric[feature])
    plt.title(f'Box Plot of {feature}')
    plt.show()

scaler = StandardScaler()
cleanDatas[num_cols] = scaler.fit_transform(cleanDatas[numeric_columns.columns])
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
sns.histplot(cleanDatas[num_cols[0]], kde=True)
plt.title("Histogram Sebelum Standardisasi")

plt.subplot(1, 2, 2)
sns.histplot(cleanDatas[num_cols[0]], kde=True)
plt.title("Histogram Setelah Standardisasi")